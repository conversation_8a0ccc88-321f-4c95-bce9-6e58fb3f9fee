@echo off
echo ========================================
echo Compiling VisionGuard Review Response Document
echo ========================================
echo.

echo Step 1: First LaTeX compilation...
pdflatex Review_Response_Document.tex

echo.
echo Step 2: Processing bibliography (if needed)...
bibtex Review_Response_Document

echo.
echo Step 3: Second LaTeX compilation...
pdflatex Review_Response_Document.tex

echo.
echo Step 4: Final LaTeX compilation...
pdflatex Review_Response_Document.tex

echo.
echo ========================================
echo Compilation complete! 
echo Check Review_Response_Document.pdf
echo ========================================
echo.

echo Files created:
echo - Review_Response_Document.pdf (Main review response)
echo.

echo This document compares:
echo - Main.tex (Original submission - before review)
echo - VisionGuard_Enhanced_Main.tex (Revised submission - after review)
echo.

echo Key improvements addressed:
echo 1. Door state detection with practical limitations
echo 2. Corrected equation numbering (equations 1-13)
echo 3. Enhanced user study (5 to 12 participants)
echo 4. Comprehensive use case scenarios with figures
echo 5. Detailed ergonomic analysis and safety discussion
echo 6. Professional formatting and figure improvements
echo.

pause
