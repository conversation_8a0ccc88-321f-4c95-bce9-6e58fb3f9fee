@echo off
echo ========================================
echo VisionGuard Submission Files Verification
echo ========================================
echo.

echo Checking required PDF files...
if exist "Befor_Review.pdf" (
    echo ✓ Befor_Review.pdf - Original submission found
) else (
    echo ✗ Befor_Review.pdf - MISSING!
)

if exist "After_review.pdf" (
    echo ✓ After_review.pdf - Revised submission found
) else (
    echo ✗ After_review.pdf - MISSING!
)

echo.
echo Checking LaTeX source files...
if exist "Main.tex" (
    echo ✓ Main.tex - Original source found
) else (
    echo ✗ Main.tex - MISSING!
)

if exist "VisionGuard_Enhanced_Main.tex" (
    echo ✓ VisionGuard_Enhanced_Main.tex - Enhanced source found
) else (
    echo ✗ VisionGuard_Enhanced_Main.tex - MISSING!
)

if exist "Review_Response_Document.tex" (
    echo ✓ Review_Response_Document.tex - Review response found
) else (
    echo ✗ Review_Response_Document.tex - MISSING!
)

echo.
echo Checking supporting files...
if exist "figures\" (
    echo ✓ figures/ directory found
    dir /b figures\*.png 2>nul | find /c ".png" > temp_count.txt
    set /p fig_count=<temp_count.txt
    del temp_count.txt
    echo   - Contains professional figures for enhanced paper
) else (
    echo ✗ figures/ directory - MISSING!
)

if exist "references.bib" (
    echo ✓ references.bib - Bibliography found
) else (
    echo ✗ references.bib - MISSING!
)

if exist "create_figures.py" (
    echo ✓ create_figures.py - Figure generation script found
) else (
    echo ✗ create_figures.py - MISSING!
)

echo.
echo ========================================
echo SUBMISSION PACKAGE SUMMARY
echo ========================================
echo.
echo FOR REVIEWERS:
echo 1. Befor_Review.pdf  - Original submission
echo 2. After_review.pdf  - Revised submission with all improvements
echo 3. Review_Response_Document.pdf - This comprehensive response
echo.
echo KEY IMPROVEMENTS IN After_review.pdf:
echo • Door state detection (open/closed) with 91.2%% accuracy
echo • Enhanced user study: 5 → 12 participants
echo • Comprehensive use case scenarios with 5 professional figures
echo • Corrected equation numbering (equations 1-13)
echo • Detailed ergonomic analysis and safety discussion
echo • Honest discussion of locked door detection limitations
echo.
echo IMPORTANT NOTE:
echo Locked door detection is practically impossible through vision alone
echo without physical interaction - this limitation is clearly addressed
echo in the revised paper with alternative solutions proposed.
echo.
echo To compile the review response document:
echo   compile_review_response.bat
echo.
echo To compile the enhanced paper:
echo   compile_paper.bat
echo.
pause
