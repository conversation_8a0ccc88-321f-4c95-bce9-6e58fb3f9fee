\documentclass[11pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algpseudocode}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{algorithm}
\usepackage{algpseudocode}
\usepackage{subfig}
\usepackage{listings}
\usepackage{tikz}
\usepackage{float}
\usepackage[margin=1in]{geometry}
\usepackage{authblk}

% Define colors for hyperlinks
\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,
    urlcolor=cyan,
    pdftitle={Vision Guard: A Computer Vision System for Assisting Visually Impaired People},
    pdfauthor={Author Names},
    pdfsubject={Computer Vision, Assistive Technology},
    pdfkeywords={computer vision, assistive technology, object detection, YOLOv8, depth estimation}
}

% Define code listing style
\lstset{
    basicstyle=\ttfamily\small,
    keywordstyle=\color{blue},
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    numbers=left,
    numberstyle=\tiny,
    numbersep=5pt,
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    showstringspaces=false
}

\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}

% Include cover page content
\begin{document}

% Cover page

\title{\LARGE \bf Vision Guard: Computer Vision-Based Door Detection and Navigation Assistance for the Visually Impaired}

\author[1]{Polok Poddar}
\author[2]{Mourika Nigar Mouny}
\author[3]{Labib Hasan Khan}
\author[4]{Md Sabbir Hossain}
\author[5]{Annajiat Alim Rasel}

\affil[1-5]{BRAC University, Dhaka, Bangladesh}

\date{May 15, 2025}

\maketitle

\begin{center}
    \textbf{Emails:}\\
    \href{mailto:<EMAIL>}{<EMAIL>}, 
    \href{mailto:<EMAIL>}{<EMAIL>}, 
    \href{mailto:<EMAIL>}{<EMAIL>}, 
    \href{mailto:<EMAIL>}{<EMAIL>}, 
    \href{mailto:<EMAIL>}{<EMAIL>}
\end{center}

\begin{abstract}
Navigation in unfamiliar spaces is highly challenging for the visually impaired, especially when door detection and localization become life-critical during earthquake or fire incidents in addition to daily life. Vision Guard is a novel computer vision system introduced in this paper that offers real-time door detection, door state classification, comprehensive obstacle detection, and navigation using just one camera with depth estimation. The system integrates a dual-model architecture combining a custom-trained YOLOv8 door detection model with a pre-trained YOLOv8n obstacle detection model, enhanced with monocular depth estimation methods to detect doors, classify their states (open/closed), identify 80+ types of obstacles, measure distances, and provide voice-guided navigation. Our enhanced solution achieves 95\% accuracy in door state detection, 94\% accuracy in detecting doors and components (handle, knob, hinge), comprehensive obstacle detection across 80+ object classes, and averages less than 10 cm error for distance measurements. We examine how the system functions under various indoor conditions and lighting configurations and present it as an effective assistive technology. Vision Guard offers a convenient, affordable and easy-to-use solution to enhance the mobility and autonomy of visually impaired users without requiring special equipment or environmental modification.
\end{abstract}

\textbf{Keywords:} computer vision, assistive technology, object detection, YOLOv8, depth estimation, visually impaired, door detection, door state classification, obstacle detection, navigation assistance

\section{Introduction}
\subsection{Background and Motivation}
As reported by the World Health Organization, 285 million people are visually impaired, and 39 million are blind \cite{who_vision}. Indoor movement is still a significant challenge for this community, particularly in the detection and location of doorways and the classification of their states (open or closed), which represent fundamental transitions between spatial regions. The ability to recognize door states and navigate around obstacles independently is an essential aspect of independent mobility and is a very important factor in facilitating the daily activity of visually impaired persons.

\subsection{Problem Statement}
There are various drawbacks to the assistive technology used today for door detection and navigation:

\begin{enumerate}
    \item Significant reliance on multiple sensors, including RGB-D cameras, LiDAR, and ultrasonic modules, which raises the cost and complexity of the system's architecture.
    \item Environmental modification requirements, including the placement of RFID tags or Bluetooth beacons, may not be possible in all settings.
    \item Limited real-time processing capability, especially for devices with limited resources.
    \item Reduced detection accuracy in various interior situations and lighting conditions.
    \item Lack of comprehensive obstacle detection beyond basic door detection.
    \item Absence of door state classification (open vs. closed) which is critical for navigation decisions.
    \item Accessibility and usability are hampered by user interfaces that are frequently complicated and demand extensive training.
\end{enumerate}

Due to these constraints, there is a substantial disconnect between lab-based models and workable, reasonable solutions that the blind and visually handicapped can use on a daily basis.

\subsection{Proposed Solution}
This paper presents Vision Guard, a single-camera computer vision system that overcomes these limitations with enhanced door detection, door state classification, comprehensive obstacle detection, and navigation aid in real-time. Vision Guard combines a dual-model architecture using custom door detection with pre-trained obstacle detection, advanced depth estimation algorithms, and intelligent door state classification to detect doors, determine their states, identify obstacles across 80+ object classes, measure distances, and provide directional assistance through voice alerts.

\subsection{Key Contributions}
The key contributions of this research include:
\begin{itemize}
    \item A dual-model architecture combining custom YOLOv8 door detection with pre-trained YOLOv8n obstacle detection, achieving 94\% door detection accuracy and comprehensive obstacle coverage across 80+ object classes.
    \item A novel door state classification system using multi-algorithm approach (two-knob detection, similarity matching with 166 reference images, and Random Forest classifier) achieving 95\% accuracy in determining door states (open/closed).
    \item Enhanced single-camera depth estimation for accurate distance measurement with average error less than 10 cm.
    \item A comprehensive door dataset with 5,000 images capturing diverse door types, lighting conditions, and perspectives, plus 166 reference images for open door classification.
    \item An integrated system architecture that combines detection, state classification, obstacle detection, depth estimation, and voice guidance in a real-time application running at 10+ FPS on standard hardware.
\end{itemize}

\subsection{Structure of the paper}
The following sections of this paper are structured as follows: 
Section \ref{sec:related_work} reviews related work in assistive technologies for those who are visually impaired. Section \ref{sec:methodology} details the methodology including dataset creation, dual-model architecture, door state classification, and system implementation. Section \ref{sec:results} presents experimental results and performance evaluation. Section \ref{sec:discussion} discusses the implications and limitations of our approach, and Section \ref{sec:conclusion} concludes with future research directions.

\section{Related Work}
\label{sec:related_work}
\subsection{Assistive Technologies for the Visually Impaired}

In recent decades, traditional aids for the visually impaired, including white canes and guide dogs, have been supplemented with a number of electronic travel aids (ETA) \cite{whitecane, eta_survey}. These are technologies that vary from obstacle detectors to complex systems that give spatial awareness and navigation guidance \cite{manduchi}.

Computer vision-based assistive systems have gained prominence due to their ability to extract rich semantic information from the environment \cite{deep_assist}. These systems can detect certain objects of interest, such as doors, stairs, and pedestrian crossings, which can provide contextual information other than obstacle detection \cite{tian_door}.

\subsection{Door Detection Systems}
Detecting doors is an essential aspect of indoor navigation systems for visually impaired individuals. Early approaches relied on simple features such as edge detection and color segmentation \cite{tian_door}. Recent methods leverage deep learning techniques to achieve higher accuracy and robustness.

Chen and Huang \cite{chen2018door} proposed a door detection system that combines RGB and depth information using a Kinect sensor. Liu et al. \cite{liu2019deep} developed a CNN-based approach to door detection in various indoor environments. Lin et al. \cite{lin2022door} used YOLOv5 for door detection in real time, achieving promising results but still requiring specialized hardware for depth perception.

However, existing systems typically focus only on door detection without considering door state classification (open vs. closed) or comprehensive obstacle detection, which are critical for safe navigation.

\subsection{Depth Estimation Techniques}
Precise depth estimation is important for navigation assistance. Conventional methods use special hardware, such as stereo cameras \cite{stereo_vision}, RGB-D sensors \cite{kinect}, or LiDAR. Although effective, these solutions add to the cost, size, and power consumption of the system.

Recent progress in monocular depth estimation \cite{mono_depth} has made depth perception possible from a single camera. Eigen et al. \cite{eigen} suggested a multiscale deep network for depth prediction from a single image. Based on this work, Godard et al. \cite{godard} presented an unsupervised method based on the consistency of left-right.

\subsection{Voice-Based Guidance Systems}
Voice interfaces are very suitable for visually impaired users. Systems such as NavCog \cite{navcog} give turn-by-turn navigation instructions via audio feedback. Ahmetovic et al. \cite{ahmetovic} proved that effective voice commands can boost navigation efficiency for the visually impaired.

Although these previous works have made important contributions, they usually require the use of several sensors, specialized hardware, or environmental changes. Vision Guard overcomes these limitations by combining door detection, door state classification, comprehensive obstacle detection, depth estimation, and voice guidance into a single-camera system designed for real-world applications.

\section{Methodology}
\label{sec:methodology}
\subsection{System Overview}
Vision Guard has five main components: (1) a dual-model detection system using custom YOLOv8 for doors and pre-trained YOLOv8n for obstacles, (2) a door state classification module using multi-algorithm approach, (3) a depth estimation module for distance calculation, (4) a comprehensive obstacle detection module covering 80+ object classes, and (5) a voice guidance system. The architecture of the system is shown in Figure~\ref{fig:system_architecture}.

\begin{figure}[H]
    \centering
    \includegraphics[width=0.30\linewidth]{).png}
    \caption{Enhanced System Overview with Dual-Model Architecture}
    \label{fig:system_architecture}
\end{figure}

The system processes video frames from a single camera, detects doors and their states, identifies obstacles across multiple categories, estimates distances, and provides voice commands to instruct the user. The processing pipeline is optimized for real-time processing on portable devices.

\subsection{Enhanced System Architecture Details}

The Vision Guard system consists of several interconnected modules, each responsible for a specific aspect of the enhanced door detection, state classification, and navigation process:

\begin{enumerate}
    \item \textbf{Input Module:} Captures video frames from a standard RGB camera at 30 frames per second with a resolution of 1280×720 pixels.

    \item \textbf{Pre-processing Module:} Prepares images for the dual detection models by resizing frames to 640×640 pixels (YOLOv8 input size), normalizing pixel values to the range [0,1], applying color correction to adapt to varying lighting conditions and converting color space from BGR to RGB.

    \item \textbf{Dual Detection Module:} Implements two YOLOv8 models:
    \begin{itemize}
        \item \textbf{Custom Door Model:} Detects doors and components (handles, knobs, hinges, levers)
        \item \textbf{Pre-trained Obstacle Model:} Detects 80+ object classes from COCO dataset (people, furniture, vehicles, etc.)
    \end{itemize}

    \item \textbf{Door State Classification Module:} Determines door state (open/closed) using:
    \begin{itemize}
        \item Two-knob detection rule (if 2+ knobs visible → door is open)
        \item Similarity matching with 166 reference open door images
        \item Random Forest classifier with comprehensive feature extraction
        \item Confidence-weighted fusion of multiple estimates
    \end{itemize}

    \item \textbf{Depth Estimation Module:} Calculates distances to detected objects using monocular depth estimation network, geometric constraints based on known object dimensions, statistical filtering to remove outliers and confidence-weighted fusion of multiple estimates.

    \item \textbf{Enhanced Navigation Module:} Processes detection, state classification, and depth information to determine optimal path considering door states, identify potential obstacles across 80+ categories, calculate directional guidance (angle and distance), and prioritize navigation targets based on safety and user preferences.

    \item \textbf{Voice Guidance Module:} Converts navigation information into audio feedback that generates clear, concise voice commands including door state information, prioritizes urgent warnings (e.g., imminent collision), adjusts verbosity based on user preferences, and provides confirmation of successful detections.
\end{enumerate}

The system operates in a continuous loop, processing frames at approximately 15-30 FPS depending on the hardware platform. A thread management system ensures that voice commands do not overlap and that critical information is delivered to the user without delay.

\subsection{Dataset Creation}
To train our door detection model, we have developed a comprehensive dataset of 5,000 images of doors in different indoor settings. The dataset includes:
\begin{itemize}
    \item Different types of doors (wooden, glass, metal, automatic).
    \item Different lighting conditions (bright, dim, backlit).
    \item Multiple views (frontal, angled, obscured).
    \item Different door components (handles, knobs, hinges, levers).
    \item Different environments (offices, homes, public buildings).
    \item Door state variations (open, closed, partially open).
\end{itemize}

Additionally, we collected 166 reference images specifically of open doors for the door state classification system. Images were manually labeled with bounding boxes for doors and their parts in the YOLO format. The dataset was partitioned into training (70\%), validation (15\%), and testing (15\%) sets with balanced representation of categories.

\subsection{Dual-Model Door and Obstacle Detection}
We implemented a dual-model architecture that combines specialized door detection with comprehensive obstacle detection:

\subsubsection{Custom Door Detection Model}
We used YOLOv8 as our base detection framework for door-specific detection due to its excellent balance of speed and accuracy. The model was initialized with pretrained weights on COCO dataset and fine-tuned on our custom door dataset.

The network architecture includes a CSPDarknet53 backbone, PANet neck for feature aggregation, and detection head. We modified the final layer to recognize our classes: door, handle, knob, hinge and lever.

Key elements of our door detection implementation:
\begin{itemize}
    \item Base model: YOLOv8n (nano) with 3.2 million parameters
    \item Input resolution: 640×640 pixels
    \item Anchor-free detection with direct bounding box coordinates prediction
    \item Multi-class classification for 5 classes (door, knob, lever, hinge, handle)
\end{itemize}

\subsubsection{Pre-trained Obstacle Detection Model}
For comprehensive obstacle detection, we utilize the pre-trained YOLOv8n model trained on the COCO dataset, which provides detection capabilities for 80+ object classes including:
\begin{itemize}
    \item People and animals (person, cat, dog, etc.)
    \item Vehicles (car, bicycle, motorcycle, etc.)
    \item Furniture (chair, couch, table, etc.)
    \item Electronics (tv, laptop, cell phone, etc.)
    \item Household items (bottle, cup, book, etc.)
\end{itemize}

This dual-model approach ensures both specialized door detection accuracy and comprehensive environmental awareness for safe navigation.

\subsection{Enhanced Door State Classification}
A critical innovation in our system is the multi-algorithm door state classification that determines whether a detected door is open or closed. This information is crucial for navigation decisions.

\subsubsection{Multi-Algorithm Approach}
Our door state classification employs four complementary algorithms in priority order:

\textbf{1. Two-Knob Detection Rule (Highest Priority):}
Based on the physical principle that when a door is open, both the front and back knobs become visible to the camera. If two or more knobs are detected simultaneously, the door is classified as open with 95\% confidence.

\textbf{2. Similarity Matching with Reference Images:}
The system compares the detected door region with 166 reference images of open doors using histogram correlation analysis. If similarity exceeds 75\%, the door is classified as open.

\textbf{3. Random Forest Classifier:}
A trained machine learning model uses comprehensive feature extraction (image statistics, edge detection, histogram features, texture analysis) to classify door state with confidence scoring.

\textbf{4. Fallback Reference Comparison:}
If the trained classifier is unavailable, the system relies solely on reference image similarity with adjusted thresholds.

\subsection{Mathematical Formulation of Depth Estimation}
Our depth estimation approach combines monocular depth prediction with geometric constraints specific to doors. The monocular depth estimation network $f_\theta$ predicts a depth map $D$ from a single RGB image $I$:

\begin{equation}
D = f_\theta(I)
\end{equation}

where $\theta$ represents the network parameters learned during training. The network architecture follows an encoder-decoder structure with skip connections, where the encoder $E$ extracts features at multiple scales, and the decoder $G$ reconstructs the depth map:

\begin{equation}
D = G(E(I))
\end{equation}

The network is trained to minimize a combination of reconstruction loss $\mathcal{L}_{rec}$ and smoothness loss $\mathcal{L}_{smooth}$:

\begin{equation}
\mathcal{L} = \mathcal{L}_{rec} + \lambda \mathcal{L}_{smooth}
\end{equation}

where $\lambda$ is a weighting factor. The reconstruction loss is defined as:

\begin{equation}
\mathcal{L}_{rec} = \frac{1}{N} \sum_{i=1}^{N} \left( \alpha \cdot \frac{|D_i - D_i^*|}{D_i^*} + (1-\alpha) \cdot \frac{|D_i - D_i^*|^2}{D_i^*} \right)
\end{equation}

where $D_i$ is the predicted depth in pixel $i$, $D_i^*$ is the depth of the ground truth, $N$ is the number of pixels, and $\alpha$ is a parameter that balances the scale-invariant and scale-dependent terms.

For door detection specifically, we refine the depth estimate using geometric constraints. Given a detected door with a bounding box width $w_{px}$ in pixels and assuming a standard door width $W_{real}$ (typically 0.85m), we can estimate the distance $Z$ using the pinhole camera model:

\begin{equation}
Z = \frac{f \cdot W_{real}}{w_{px}}
\end{equation}

where $f$ is the focal length of the camera in pixels. This geometric estimate $Z_{geo}$ is combined with the monocular depth prediction $Z_{mono}$ using a weighted average:

\begin{equation}
Z_{final} = \beta \cdot Z_{geo} + (1-\beta) \cdot Z_{mono}
\end{equation}

where $\beta$ is a confidence factor that depends on the door detection confidence score $c$:

\begin{equation}
\beta = \min(1, \max(0, \gamma \cdot c - \delta))
\end{equation}

with $\gamma$ and $\delta$ being hyperparameters that control the influence of the detection confidence on the weighting.

To handle potential outliers in the depth map, we apply statistical filtering to the depth values within the door bounding box. Let $\mathcal{D}_{door}$ be the set of depth values within the door region. We compute the median $\tilde{D}$ and the median absolute deviation (MAD):

\begin{equation}
\text{MAD} = \text{median}(|D_i - \tilde{D}|) \quad \forall D_i \in \mathcal{D}_{door}
\end{equation}

We then filter out values that deviate significantly from the median:

\begin{equation}
\mathcal{D}_{filtered} = \{D_i \in \mathcal{D}_{door} : |D_i - \tilde{D}| < k \cdot \text{MAD}\}
\end{equation}

where $k$ is a threshold parameter (typically set to 2.5). The final monocular depth estimate $Z_{mono}$ is computed as the mean of the filtered values:

\begin{equation}
Z_{mono} = \frac{1}{|\mathcal{D}_{filtered}|} \sum_{D_i \in \mathcal{D}_{filtered}} D_i
\end{equation}

\subsection{Comprehensive Obstacle Detection and Avoidance}
Our enhanced system provides comprehensive obstacle detection beyond door detection, utilizing the pre-trained YOLOv8n model to identify 80+ object classes from the COCO dataset. This ensures complete environmental awareness for safe navigation.

\subsubsection{Obstacle Classification and Processing}
The obstacle detection module processes each frame to identify potential hazards and navigation aids:

\textbf{High-Priority Obstacles (Immediate Danger):}
\begin{itemize}
    \item People (moving or stationary)
    \item Vehicles (cars, bicycles, motorcycles)
    \item Large furniture (tables, chairs, couches)
\end{itemize}

\textbf{Medium-Priority Obstacles (Navigation Considerations):}
\begin{itemize}
    \item Small furniture and objects
    \item Electronics and appliances
    \item Household items
\end{itemize}

\textbf{Low-Priority Objects (Environmental Context):}
\begin{itemize}
    \item Decorative items
    \item Books and small objects
    \item Background elements
\end{itemize}

\subsubsection{Mathematical Formulation of Obstacle Avoidance}
We model the user's path as a vector $\vec{p}$ pointing from the user's current position to the target door. For each detected obstacle with position vector $\vec{o}$ relative to the user, we calculate the projection of the obstacle onto the path vector:

\begin{equation}
\text{proj}_{\vec{p}}(\vec{o}) = \frac{\vec{o} \cdot \vec{p}}{|\vec{p}|^2} \vec{p}
\end{equation}

The perpendicular distance from the obstacle to the path is then:

\begin{equation}
d_{\perp} = |\vec{o} - \text{proj}_{\vec{p}}(\vec{o})|
\end{equation}

We define a safety threshold $d_{\text{safe}}$ based on the physical dimensions of the obstacle and a safety margin:

\begin{equation}
d_{\text{safe}} = r_{\text{obstacle}} + r_{\text{user}} + \text{margin}
\end{equation}

where $r_{\text{obstacle}}$ is the estimated radius of the obstacle, $r_{\text{user}}$ is the user's personal space radius (typically 0.5m), and margin is an additional safety buffer (typically 0.3m).

An obstacle is considered to be in the user's path if:

\begin{equation}
d_{\perp} < d_{\text{safe}} \quad \text{and} \quad 0 \leq \frac{\vec{o} \cdot \vec{p}}{|\vec{p}|^2} \leq 1
\end{equation}

For dynamic obstacles, we incorporate a velocity vector $\vec{v}_{\text{obstacle}}$ and predict the future position of the obstacle at time $t$:

\begin{equation}
\vec{o}(t) = \vec{o}(0) + \vec{v}_{\text{obstacle}} \cdot t
\end{equation}

\subsubsection{Enhanced Navigation Algorithm}
Our navigation algorithm integrates door detection, door state classification, and comprehensive obstacle detection:

\begin{algorithm}[H]
\caption{Enhanced Navigation with Door State and Obstacle Detection}
\begin{algorithmic}[1]
\State \textbf{Input:} Video frame $I$, user position $P_{user}$
\State \textbf{Output:} Navigation command with door state and obstacle information

\State $doors \leftarrow$ CustomYOLOv8\_DetectDoors($I$)
\State $obstacles \leftarrow$ PretrainedYOLOv8n\_DetectObstacles($I$)

\For{each $door$ in $doors$}
    \State $state \leftarrow$ ClassifyDoorState($door$, $I$)
    \State $distance \leftarrow$ EstimateDepth($door$, $I$)
    \State $door.state \leftarrow state$
    \State $door.distance \leftarrow distance$
\EndFor

\For{each $obstacle$ in $obstacles$}
    \State $distance \leftarrow$ EstimateDepth($obstacle$, $I$)
    \State $priority \leftarrow$ AssignPriority($obstacle.class$)
    \State $obstacle.distance \leftarrow distance$
    \State $obstacle.priority \leftarrow priority$
\EndFor

\State $path\_obstacles \leftarrow$ FilterObstaclesInPath($obstacles$, $P_{user}$)
\State $target\_door \leftarrow$ SelectOptimalDoor($doors$)

\If{$target\_door.state = \text{open}$ and $path\_obstacles = \emptyset$}
    \State \Return GenerateCommand("Navigate to open door", $target\_door.distance$)
\ElsIf{$target\_door.state = \text{closed}$}
    \State \Return GenerateCommand("Door is closed, approach carefully", $target\_door.distance$)
\ElsIf{$path\_obstacles \neq \emptyset$}
    \State $urgent\_obstacle \leftarrow$ GetHighestPriority($path\_obstacles$)
    \State \Return GenerateCommand("Obstacle detected", $urgent\_obstacle$)
\Else
    \State \Return GenerateCommand("Navigate to door", $target\_door.distance$)
\EndIf
\end{algorithmic}
\end{algorithm}

\subsubsection{Voice Command Generation}
The voice guidance system transforms spatial information into intuitive directional commands based on a clock face model, which has been proven to be useful for visually impaired users. The direction to a target is assigned to a clock position (1-12) depending on the angle with respect to the current orientation of the user:

\begin{equation}
\text{clock\_position} = \text{round}\left(\frac{\theta \cdot 6}{\pi} + 12\right) \bmod 12
\end{equation}

where $\theta$ is the angle in radians between the user's forward direction and the vector to the target, with $\theta = 0$ corresponding to straight ahead. If the result is 0, it is converted to 12.

Enhanced voice commands now include door state information and obstacle warnings:
\begin{itemize}
    \item "Open door detected at 12 o'clock, 3 meters ahead"
    \item "Closed door at 2 o'clock, 4 meters - approach carefully"
    \item "Person detected at 10 o'clock, 2 meters - obstacle in path"
    \item "Chair obstacle at 1 o'clock, 1.5 meters - navigate around"
\end{itemize}

\section{Results}
\label{sec:results}

\subsection{Experimental Setup}
We evaluated Vision Guard on a comprehensive test dataset consisting of 750 images (15\% of our total dataset) with diverse indoor environments, lighting conditions, and door types. The system was tested on:
\begin{itemize}
    \item Hardware: Intel Core i7-10750H CPU, NVIDIA GTX 1660 Ti GPU, 16GB RAM
    \item Camera: Standard USB webcam with 1280×720 resolution at 30 FPS
    \item Operating System: Windows 10 with Python 3.8 and PyTorch 1.12
    \item Test environments: Office buildings, residential homes, public facilities
    \item Lighting conditions: Natural daylight, artificial lighting, low-light scenarios
\end{itemize}

\subsection{Door Detection Performance}
Our custom YOLOv8 door detection model achieved excellent performance across all metrics:

\begin{table}[H]
\centering
\caption{Door Detection Performance Results}
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Class} & \textbf{Precision} & \textbf{Recall} & \textbf{F1-Score} & \textbf{mAP@0.5} \\
\hline
Door & 0.96 & 0.93 & 0.94 & 0.94 \\
Handle & 0.92 & 0.89 & 0.90 & 0.89 \\
Knob & 0.94 & 0.91 & 0.92 & 0.91 \\
Hinge & 0.88 & 0.85 & 0.86 & 0.85 \\
Lever & 0.90 & 0.87 & 0.88 & 0.87 \\
\hline
\textbf{Overall} & \textbf{0.92} & \textbf{0.89} & \textbf{0.90} & \textbf{0.89} \\
\hline
\end{tabular}
\label{tab:door_detection}
\end{table}

\subsection{Door State Classification Results}
The multi-algorithm door state classification system demonstrated superior performance:

\begin{table}[H]
\centering
\caption{Door State Classification Performance}
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Method} & \textbf{Accuracy} & \textbf{Precision} & \textbf{Recall} \\
\hline
Two-Knob Detection & 0.98 & 0.97 & 0.96 \\
Similarity Matching & 0.87 & 0.85 & 0.89 \\
Random Forest Classifier & 0.91 & 0.90 & 0.92 \\
Combined Multi-Algorithm & \textbf{0.95} & \textbf{0.94} & \textbf{0.96} \\
\hline
\end{tabular}
\label{tab:door_state}
\end{table}

\subsection{Obstacle Detection Performance}
The pre-trained YOLOv8n model provided comprehensive obstacle detection across 80+ object classes:

\begin{table}[H]
\centering
\caption{Obstacle Detection Performance by Category}
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Category} & \textbf{Classes} & \textbf{Average Precision} & \textbf{Average Recall} \\
\hline
People \& Animals & 8 & 0.89 & 0.87 \\
Vehicles & 8 & 0.85 & 0.83 \\
Furniture & 15 & 0.82 & 0.80 \\
Electronics & 12 & 0.78 & 0.76 \\
Household Items & 25 & 0.75 & 0.73 \\
Other Objects & 12 & 0.73 & 0.71 \\
\hline
\textbf{Overall (80+ classes)} & \textbf{80+} & \textbf{0.80} & \textbf{0.78} \\
\hline
\end{tabular}
\label{tab:obstacle_detection}
\end{table}

\subsection{Depth Estimation Accuracy}
Distance measurement accuracy was evaluated across different ranges:

\begin{table}[H]
\centering
\caption{Depth Estimation Performance}
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Distance Range} & \textbf{Mean Error (cm)} & \textbf{Std Dev (cm)} & \textbf{Max Error (cm)} \\
\hline
0.5 - 2.0 meters & 6.2 & 4.1 & 15.3 \\
2.0 - 4.0 meters & 8.7 & 6.2 & 22.1 \\
4.0 - 6.0 meters & 12.4 & 8.9 & 28.7 \\
6.0+ meters & 18.2 & 12.3 & 35.4 \\
\hline
\textbf{Overall} & \textbf{9.8} & \textbf{7.1} & \textbf{35.4} \\
\hline
\end{tabular}
\label{tab:depth_estimation}
\end{table}

\subsection{System Performance Metrics}
Real-time performance evaluation on standard hardware:

\begin{table}[H]
\centering
\caption{System Performance Metrics}
\begin{tabular}{|l|c|}
\hline
\textbf{Metric} & \textbf{Value} \\
\hline
Average FPS & 12.3 \\
Door Detection Time (ms) & 45.2 \\
Obstacle Detection Time (ms) & 52.1 \\
Door State Classification Time (ms) & 23.7 \\
Depth Estimation Time (ms) & 31.4 \\
Total Processing Time per Frame (ms) & 152.4 \\
Memory Usage (MB) & 1,247 \\
CPU Usage (\%) & 68.3 \\
GPU Usage (\%) & 45.7 \\
\hline
\end{tabular}
\label{tab:performance}
\end{table}

\subsection{Comparative Analysis}
Comparison with existing assistive navigation systems:

\begin{table}[H]
\centering
\caption{Comparison with Existing Systems}
\begin{tabular}{|l|c|c|c|c|c|}
\hline
\textbf{System} & \textbf{Sensors} & \textbf{Door Detection} & \textbf{Door State} & \textbf{Obstacles} & \textbf{Real-time} \\
\hline
Chen et al. \cite{chen2018door} & RGB-D & 0.87 & No & Limited & No \\
Liu et al. \cite{liu2019deep} & RGB & 0.89 & No & No & Yes \\
Lin et al. \cite{lin2022door} & RGB + Depth & 0.91 & No & Basic & Yes \\
NavCog \cite{navcog} & Multiple & No & No & Yes & Yes \\
\textbf{Vision Guard} & \textbf{RGB only} & \textbf{0.94} & \textbf{0.95} & \textbf{80+ classes} & \textbf{Yes} \\
\hline
\end{tabular}
\label{tab:comparison}
\end{table}

\section{Discussion}
\label{sec:discussion}

\subsection{Key Findings and Innovations}
Our experimental results demonstrate that Vision Guard successfully addresses the limitations of existing assistive navigation systems through several key innovations:

\textbf{1. Dual-Model Architecture Success:} The combination of custom door detection (94\% accuracy) with pre-trained obstacle detection (80+ classes) provides comprehensive environmental awareness while maintaining real-time performance. This approach eliminates the need for multiple specialized sensors.

\textbf{2. Door State Classification Breakthrough:} The multi-algorithm approach achieving 95\% accuracy in door state classification represents a significant advancement. The two-knob detection rule (98\% accuracy when applicable) provides a reliable physical principle for open door detection, while the similarity matching and Random Forest classifier ensure robust performance across diverse scenarios.

\textbf{3. Single-Camera Depth Estimation:} Our geometric constraint-enhanced monocular depth estimation achieves less than 10 cm average error, making it suitable for navigation assistance without requiring expensive depth sensors.

\textbf{4. Comprehensive Obstacle Detection:} The integration of 80+ object classes from COCO dataset provides unprecedented environmental awareness for visually impaired navigation, covering people, vehicles, furniture, and household items.

\subsection{Performance Analysis}
The system demonstrates excellent performance across multiple metrics:

\textbf{Detection Accuracy:} Door detection accuracy of 94\% and door state classification accuracy of 95\% exceed the performance of existing single-camera systems. The obstacle detection performance of 80\% average precision across 80+ classes provides comprehensive environmental awareness.

\textbf{Real-time Processing:} With 12.3 FPS average performance on standard hardware, the system meets real-time requirements for navigation assistance. The total processing time of 152.4 ms per frame allows for responsive user interaction.

\textbf{Distance Accuracy:} The depth estimation accuracy of 9.8 cm average error is sufficient for navigation guidance, with particularly good performance in the critical 0.5-4 meter range where most navigation decisions occur.

\subsection{Practical Implications}
Vision Guard offers several practical advantages for visually impaired users:

\textbf{Accessibility:} The single-camera requirement makes the system accessible and affordable, requiring only a standard smartphone or tablet with camera capability.

\textbf{Independence:} The comprehensive detection capabilities (doors, door states, obstacles) enable greater independence in unfamiliar environments without requiring environmental modifications.

\textbf{Safety:} The multi-class obstacle detection and door state classification significantly enhance safety by providing advance warning of potential hazards and navigation obstacles.

\textbf{Usability:} The voice guidance system with clock-face directions and door state information provides intuitive navigation commands that require minimal training.

\subsection{Limitations and Challenges}
Despite the strong performance, several limitations remain:

\textbf{1. Lighting Dependency:} Performance degrades in extremely low-light conditions or high-contrast scenarios. Future work should incorporate infrared or thermal imaging capabilities.

\textbf{2. Computational Requirements:} While optimized for real-time performance, the dual-model architecture requires significant computational resources, limiting deployment on very low-power devices.

\textbf{3. Distance Range Limitations:} Depth estimation accuracy decreases significantly beyond 6 meters, limiting long-range navigation planning.

\textbf{4. Occlusion Handling:} Partially occluded doors or obstacles may not be detected reliably, particularly in crowded environments.

\textbf{5. Environmental Variability:} Performance may vary across different architectural styles, door types, and cultural contexts not represented in the training dataset.

\subsection{Future Research Directions}
Several promising directions for future research emerge from this work:

\textbf{1. Multi-Modal Integration:} Incorporating audio cues, tactile feedback, and inertial measurement units could enhance navigation accuracy and user experience.

\textbf{2. Dynamic Environment Handling:} Developing algorithms for tracking moving obstacles and predicting their trajectories would improve safety in dynamic environments.

\textbf{3. Personalization:} Implementing user-specific preferences and learning algorithms could adapt the system to individual navigation patterns and needs.

\textbf{4. Extended Object Recognition:} Expanding detection capabilities to include stairs, elevators, emergency exits, and other critical navigation elements.

\textbf{5. Cross-Cultural Adaptation:} Training on diverse architectural styles and door types from different cultural contexts to improve global applicability.

\section{Conclusion}
\label{sec:conclusion}

This paper presents Vision Guard, a comprehensive computer vision system that significantly advances assistive technology for visually impaired navigation. Our dual-model architecture combining custom door detection with pre-trained obstacle detection, enhanced with multi-algorithm door state classification and single-camera depth estimation, achieves superior performance compared to existing systems while requiring only standard camera hardware.

\subsection{Key Contributions Summary}
The major contributions of this research include:

\begin{enumerate}
    \item \textbf{Dual-Model Detection Architecture:} Successfully combining specialized door detection (94\% accuracy) with comprehensive obstacle detection (80+ object classes) in a unified real-time system.

    \item \textbf{Novel Door State Classification:} Developing a multi-algorithm approach achieving 95\% accuracy in determining door states (open/closed), critical for navigation decision-making.

    \item \textbf{Enhanced Single-Camera System:} Demonstrating that comprehensive navigation assistance can be achieved with standard camera hardware, eliminating the need for expensive specialized sensors.

    \item \textbf{Comprehensive Dataset:} Creating a robust dataset of 5,000 door images plus 166 open door reference images, contributing to the research community.

    \item \textbf{Real-World Validation:} Proving system effectiveness through extensive testing across diverse environments, lighting conditions, and scenarios.
\end{enumerate}

\subsection{Impact and Significance}
Vision Guard represents a significant step forward in making assistive navigation technology more accessible, affordable, and effective for the visually impaired community. By requiring only standard camera hardware and achieving superior performance in door detection, door state classification, and obstacle detection, the system removes significant barriers to adoption while enhancing safety and independence.

The integration of door state information with comprehensive obstacle detection provides unprecedented environmental awareness, enabling visually impaired users to make informed navigation decisions in complex indoor environments. The real-time performance and intuitive voice guidance system ensure practical usability in daily life scenarios.

\subsection{Future Impact}
This research establishes a foundation for next-generation assistive navigation systems that can be deployed on widely available consumer devices. The demonstrated feasibility of comprehensive navigation assistance using single-camera systems opens new possibilities for integration into smartphones, tablets, and dedicated assistive devices.

The open research questions identified in this work, particularly around multi-modal integration, dynamic environment handling, and personalization, provide clear directions for continued advancement in this critical area of assistive technology.

Vision Guard demonstrates that sophisticated computer vision techniques can be successfully applied to create practical, accessible solutions that meaningfully improve the lives of visually impaired individuals, contributing to a more inclusive and accessible world.

\section*{Acknowledgments}
We thank the visually impaired community members who provided valuable feedback during system development and testing. We also acknowledge the support of BRAC University's Computer Science and Engineering Department and the research facilities that made this work possible.

\bibliographystyle{IEEEtran}
\bibliography{references}

\end{document}
